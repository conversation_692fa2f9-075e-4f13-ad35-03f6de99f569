#!/usr/bin/env python3
"""
MCP功能测试脚本 - 在容器环境中测试MCP功能
"""
import os
import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, '/app')

from functions.mcp_config_manager import mcp_config_manager
from functions.mcp_client import mcp_client_manager
from functions.mcp_tools import check_mcp_status, get_all_mcp_tools_info

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_mcp_config():
    """测试MCP配置管理"""
    print("=" * 50)
    print("测试MCP配置管理")
    print("=" * 50)
    
    # 测试配置加载
    print(f"MCP启用状态: {mcp_config_manager.mcp_enabled}")
    
    # 获取所有服务器配置
    servers = mcp_config_manager.get_all_servers()
    print(f"配置的服务器数量: {len(servers)}")
    
    for name, config in servers.items():
        print(f"- {name}: {config.get('description', 'N/A')} (启用: {config.get('enabled', False)})")
    
    return True


def test_mcp_enable():
    """测试MCP启用功能"""
    print("\n" + "=" * 50)
    print("测试MCP启用功能")
    print("=" * 50)
    
    # 启用MCP
    success = mcp_config_manager.toggle_mcp_global(True)
    print(f"启用MCP: {success}")
    print(f"MCP当前状态: {mcp_config_manager.mcp_enabled}")
    
    # 启用files服务
    success = mcp_config_manager.toggle_server_status("files", True)
    print(f"启用files服务: {success}")
    print(f"files服务状态: {mcp_config_manager.is_server_enabled('files')}")
    
    return True


def test_mcp_client():
    """测试MCP客户端连接"""
    print("\n" + "=" * 50)
    print("测试MCP客户端连接")
    print("=" * 50)
    
    # 获取files客户端
    client = mcp_client_manager.get_client("files")
    if not client:
        print("❌ 无法创建files客户端")
        return False
    
    print(f"✅ 创建files客户端成功: {type(client).__name__}")
    
    # 尝试连接
    try:
        connected = client.connect()
        print(f"连接状态: {connected}")
        
        if connected:
            print(f"可用工具数量: {len(client.tools)}")
            for tool_name, tool in client.tools.items():
                print(f"- {tool_name}: {tool.description}")
        else:
            print("❌ 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    
    return True


def test_mcp_tools():
    """测试MCP工具调用"""
    print("\n" + "=" * 50)
    print("测试MCP工具调用")
    print("=" * 50)
    
    # 测试list_directory工具
    try:
        success, result = mcp_client_manager.call_tool(
            "files", 
            "list_directory", 
            {"path": "/app"}
        )
        
        print(f"list_directory调用结果: {success}")
        if success:
            print("✅ 工具调用成功")
            if isinstance(result, dict) and "content" in result:
                content = result["content"]
                if isinstance(content, list) and content:
                    print(f"目录内容预览: {content[0] if content else 'Empty'}")
            else:
                print(f"结果类型: {type(result)}")
        else:
            print(f"❌ 工具调用失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 工具调用异常: {e}")
        return False
    
    return True


def test_mcp_status():
    """测试MCP状态检查"""
    print("\n" + "=" * 50)
    print("测试MCP状态检查")
    print("=" * 50)
    
    try:
        status = check_mcp_status()
        print("MCP状态信息:")
        print(json.dumps(status, indent=2, ensure_ascii=False))
        
        return status.get("mcp_enabled", False)
        
    except Exception as e:
        print(f"❌ 状态检查异常: {e}")
        return False


def test_mcp_tools_info():
    """测试MCP工具信息获取"""
    print("\n" + "=" * 50)
    print("测试MCP工具信息获取")
    print("=" * 50)
    
    try:
        tools_info = get_all_mcp_tools_info()
        print("MCP工具信息:")
        print(tools_info)
        
        return len(tools_info) > 0
        
    except Exception as e:
        print(f"❌ 工具信息获取异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始MCP功能测试")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"Node.js版本: ", end="")
    os.system("node --version")
    print(f"NPM版本: ", end="")
    os.system("npm --version")
    
    tests = [
        ("配置管理", test_mcp_config),
        ("启用功能", test_mcp_enable),
        ("客户端连接", test_mcp_client),
        ("工具调用", test_mcp_tools),
        ("状态检查", test_mcp_status),
        ("工具信息", test_mcp_tools_info),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ {test_name}: 异常 - {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP功能正常工作")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查MCP配置")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
