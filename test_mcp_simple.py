#!/usr/bin/env python3
"""
简单MCP测试脚本 - 快速验证MCP基本功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, '/app')

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from functions.mcp_config_manager import mcp_config_manager
        print("✅ mcp_config_manager 导入成功")
    except Exception as e:
        print(f"❌ mcp_config_manager 导入失败: {e}")
        return False
    
    try:
        from functions.mcp_client import mcp_client_manager
        print("✅ mcp_client_manager 导入成功")
    except Exception as e:
        print(f"❌ mcp_client_manager 导入失败: {e}")
        return False
    
    try:
        from functions.mcp_tools import check_mcp_status
        print("✅ mcp_tools 导入成功")
    except Exception as e:
        print(f"❌ mcp_tools 导入失败: {e}")
        return False
    
    return True


def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from functions.mcp_config_manager import mcp_config_manager
        
        # 启用MCP
        mcp_config_manager.toggle_mcp_global(True)
        print(f"✅ MCP启用状态: {mcp_config_manager.mcp_enabled}")
        
        # 启用files服务
        mcp_config_manager.toggle_server_status("files", True)
        print(f"✅ files服务启用状态: {mcp_config_manager.is_server_enabled('files')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_node_env():
    """测试Node.js环境"""
    print("\n测试Node.js环境...")
    
    # 检查Node.js
    node_result = os.system("node --version > /dev/null 2>&1")
    if node_result == 0:
        print("✅ Node.js 可用")
        os.system("echo -n 'Node.js版本: ' && node --version")
    else:
        print("❌ Node.js 不可用")
        return False
    
    # 检查NPM
    npm_result = os.system("npm --version > /dev/null 2>&1")
    if npm_result == 0:
        print("✅ NPM 可用")
        os.system("echo -n 'NPM版本: ' && npm --version")
    else:
        print("❌ NPM 不可用")
        return False
    
    return True


def test_mcp_package():
    """测试MCP包安装"""
    print("\n测试MCP包...")
    
    # 测试MCP包是否可用
    try:
        # 尝试运行npx命令检查MCP服务器包
        result = os.system("npx --help > /dev/null 2>&1")
        if result == 0:
            print("✅ npx 命令可用")
        else:
            print("❌ npx 命令不可用")
            return False
        
        # 检查文件系统MCP服务器包
        print("检查 @modelcontextprotocol/server-filesystem 包...")
        result = os.system("timeout 30 npx -y @modelcontextprotocol/server-filesystem --help > /dev/null 2>&1")
        if result == 0:
            print("✅ MCP文件系统服务器包可用")
        else:
            print("⚠️ MCP文件系统服务器包可能需要首次下载")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP包测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 MCP简单功能测试")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_imports),
        ("配置管理", test_config),
        ("Node.js环境", test_node_env),
        ("MCP包检查", test_mcp_package),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 40)
    print("测试总结")
    print("=" * 40)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 基础测试通过！可以进行完整MCP测试")
        return 0
    else:
        print("⚠️ 基础测试失败，请检查环境配置")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
