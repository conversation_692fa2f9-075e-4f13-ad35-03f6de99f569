"""
MCP配置管理器 - 简化版
"""
import json
import os
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class MCPConfigManager:
    """MCP配置管理器"""

    def __init__(self, config_path: str = "config/mcp_config.json"):
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载MCP配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if 'mcp_enabled' not in config:
                        config['mcp_enabled'] = False
                    if 'mcpServers' not in config:
                        config['mcpServers'] = {}
                    return config
            else:
                return self._create_default_config()
        except Exception as e:
            logger.error(f"加载MCP配置失败: {e}")
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "mcp_enabled": False,
            "mcpServers": {
                "files": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/app"],
                    "enabled": False,
                    "connection_type": "stdio",
                    "description": "文件系统操作工具"
                }
            }
        }

    def _save_config(self) -> bool:
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"保存MCP配置失败: {e}")
            return False

    @property
    def mcp_enabled(self) -> bool:
        """获取MCP全局开关状态"""
        return self.config.get('mcp_enabled', False)

    def toggle_mcp_global(self, enabled: bool) -> bool:
        """切换MCP全局开关"""
        try:
            self.config['mcp_enabled'] = enabled
            return self._save_config()
        except Exception as e:
            logger.error(f"切换MCP全局开关失败: {e}")
            return False

    def get_all_servers(self) -> Dict[str, Any]:
        """获取所有MCP服务配置"""
        return self.config.get('mcpServers', {})

    def get_server_config(self, server_name: str) -> Optional[Dict[str, Any]]:
        """获取指定服务的配置"""
        return self.config.get('mcpServers', {}).get(server_name)

    def is_server_enabled(self, server_name: str) -> bool:
        """检查服务是否启用"""
        server_config = self.get_server_config(server_name)
        if not server_config:
            return False
        return server_config.get('enabled', False)

    def toggle_server_status(self, server_name: str, enabled: bool) -> bool:
        """切换服务启用状态"""
        try:
            if server_name not in self.config.get('mcpServers', {}):
                logger.error(f"服务 {server_name} 不存在")
                return False

            self.config['mcpServers'][server_name]['enabled'] = enabled
            return self._save_config()
        except Exception as e:
            logger.error(f"切换服务 {server_name} 状态失败: {e}")
            return False

    def get_enabled_servers(self) -> Dict[str, Any]:
        """获取所有启用的服务"""
        if not self.mcp_enabled:
            return {}

        enabled_servers = {}
        for name, config in self.get_all_servers().items():
            if config.get('enabled', False):
                enabled_servers[name] = config
        return enabled_servers


# 全局配置管理器实例
mcp_config_manager = MCPConfigManager()
