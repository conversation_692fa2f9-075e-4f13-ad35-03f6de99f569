"""
简化MCP工具信息 - 为简化配置页面提供工具信息
"""
import logging
from .mcp_config_manager import mcp_config_manager

logger = logging.getLogger(__name__)


def get_mcp_tools_info() -> str:
    """
    获取MCP工具信息（简化版本）
    
    Returns:
        str: 格式化的工具信息
    """
    try:
        if not mcp_config_manager.mcp_enabled:
            return "**MCP功能未启用**\n\n请先启用MCP功能以查看可用工具。"
        
        enabled_servers = mcp_config_manager.get_enabled_servers()
        if not enabled_servers:
            return "**没有启用的MCP服务**\n\n请在上方启用至少一个MCP服务。"
        
        info_lines = []
        info_lines.append("## 当前可用的MCP工具")
        info_lines.append("")
        
        # 显示启用的服务
        for server_name, server_config in enabled_servers.items():
            description = server_config.get("description", "")
            connection_type = server_config.get("connection_type", "unknown")
            
            info_lines.append(f"### 📡 {server_name}")
            if description:
                info_lines.append(f"**描述**: {description}")
            info_lines.append(f"**连接类型**: {connection_type}")
            
            # 根据服务类型显示预期工具
            if server_name == "files":
                info_lines.append("**预期工具**:")
                info_lines.append("- `list_directory`: 列出目录内容")
                info_lines.append("- `read_file`: 读取文件内容")
                info_lines.append("- `write_file`: 写入文件内容")
            elif server_name == "playwright":
                info_lines.append("**预期工具**:")
                info_lines.append("- `navigate`: 导航到网页")
                info_lines.append("- `screenshot`: 截取网页截图")
                info_lines.append("- `click`: 点击页面元素")
                info_lines.append("- `type`: 输入文本")
            else:
                info_lines.append("**工具**: 启动后自动发现")
            
            info_lines.append("")
        
        info_lines.append("---")
        info_lines.append("**注意**: 工具详细信息将在首次使用时自动获取。")
        
        return "\n".join(info_lines)
        
    except Exception as e:
        logger.error(f"获取简化MCP工具信息失败: {e}")
        return f"**获取工具信息失败**: {str(e)}"


def get_server_tools_preview(server_name: str) -> str:
    """
    获取服务器工具预览
    
    Args:
        server_name: 服务器名称
        
    Returns:
        str: 工具预览信息
    """
    server_config = mcp_config_manager.get_server_config(server_name)
    if not server_config:
        return "服务器不存在"
    
    description = server_config.get("description", "")
    connection_type = server_config.get("connection_type", "unknown")
    
    preview = f"**{server_name}**\n"
    if description:
        preview += f"描述: {description}\n"
    preview += f"连接类型: {connection_type}\n"
    
    # 根据服务器类型提供工具预览
    if server_name == "files":
        preview += "主要功能: 文件系统操作\n"
        preview += "常用工具: list_directory, read_file, write_file"
    elif server_name == "playwright":
        preview += "主要功能: 浏览器自动化\n"
        preview += "常用工具: navigate, screenshot, click, type"
    elif server_name == "duckduckgo-remote":
        preview += "主要功能: 网络搜索\n"
        preview += "常用工具: search"
    elif server_name == "chinarailway":
        preview += "主要功能: 中国铁路查询\n"
        preview += "常用工具: query_train, query_ticket"
    elif server_name == "hotnews":
        preview += "主要功能: 热点新闻\n"
        preview += "常用工具: get_hot_news"
    else:
        preview += "工具信息: 启动后自动发现"
    
    return preview
