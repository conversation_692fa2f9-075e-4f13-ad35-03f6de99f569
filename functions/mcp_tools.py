"""
MCP工具调用接口 - 提供统一的MCP工具调用接口
"""
import logging
from typing import Dict, Any, List, Tuple, Optional

from .mcp_client import mcp_client_manager
from .mcp_config_manager import mcp_config_manager

logger = logging.getLogger(__name__)


def use_mcp_tool(server_name: str, tool_name: str, arguments: Dict[str, Any]) -> <PERSON><PERSON>[bool, Any]:
    """
    使用MCP工具
    
    Args:
        server_name: MCP服务器名称
        tool_name: 工具名称
        arguments: 工具参数
        
    Returns:
        Tuple[bool, Any]: (成功标志, 结果数据)
    """
    try:
        # 检查MCP是否启用
        if not mcp_config_manager.mcp_enabled:
            return False, "MCP功能未启用，请在配置页面开启MCP功能"
        
        # 检查服务器是否启用
        if not mcp_config_manager.is_server_enabled(server_name):
            return False, f"MCP服务 '{server_name}' 未启用，请在配置页面启用该服务"
        
        # 调用工具
        success, result = mcp_client_manager.call_tool(server_name, tool_name, arguments)
        
        if success:
            logger.info(f"MCP工具调用成功: {server_name}.{tool_name}")
            return True, result
        else:
            logger.error(f"MCP工具调用失败: {server_name}.{tool_name} - {result}")
            return False, f"工具调用失败: {result}"
            
    except Exception as e:
        logger.error(f"MCP工具调用异常: {server_name}.{tool_name} - {e}")
        return False, f"工具调用异常: {str(e)}"


def access_mcp_resource(server_name: str, uri: str) -> Tuple[bool, Any]:
    """
    访问MCP资源
    
    Args:
        server_name: MCP服务器名称
        uri: 资源URI
        
    Returns:
        Tuple[bool, Any]: (成功标志, 资源数据)
    """
    try:
        # 检查MCP是否启用
        if not mcp_config_manager.mcp_enabled:
            return False, "MCP功能未启用，请在配置页面开启MCP功能"
        
        # 检查服务器是否启用
        if not mcp_config_manager.is_server_enabled(server_name):
            return False, f"MCP服务 '{server_name}' 未启用，请在配置页面启用该服务"
        
        # 获取资源
        success, result = mcp_client_manager.get_resource(server_name, uri)
        
        if success:
            logger.info(f"MCP资源访问成功: {server_name} - {uri}")
            return True, result
        else:
            logger.error(f"MCP资源访问失败: {server_name} - {uri} - {result}")
            return False, f"资源访问失败: {result}"
            
    except Exception as e:
        logger.error(f"MCP资源访问异常: {server_name} - {uri} - {e}")
        return False, f"资源访问异常: {str(e)}"


def auto_use_mcp_tool(tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, Any]:
    """
    自动选择服务器使用MCP工具
    
    Args:
        tool_name: 工具名称
        arguments: 工具参数
        
    Returns:
        Tuple[bool, Any]: (成功标志, 结果数据)
    """
    try:
        # 检查MCP是否启用
        if not mcp_config_manager.mcp_enabled:
            return False, "MCP功能未启用，请在配置页面开启MCP功能"
        
        # 自动调用工具
        success, result = mcp_client_manager.auto_call_tool(tool_name, arguments)
        
        if success:
            logger.info(f"MCP工具自动调用成功: {tool_name}")
            return True, result
        else:
            logger.error(f"MCP工具自动调用失败: {tool_name} - {result}")
            return False, f"工具调用失败: {result}"
            
    except Exception as e:
        logger.error(f"MCP工具自动调用异常: {tool_name} - {e}")
        return False, f"工具调用异常: {str(e)}"


def get_all_mcp_tools_info() -> str:
    """
    获取所有MCP工具信息
    
    Returns:
        str: 格式化的工具信息
    """
    try:
        if not mcp_config_manager.mcp_enabled:
            return "MCP功能未启用"
        
        enabled_servers = mcp_config_manager.get_enabled_servers()
        if not enabled_servers:
            return "没有启用的MCP服务"
        
        info_lines = []
        info_lines.append("## 可用的MCP服务和工具")
        info_lines.append("")
        
        all_tools = mcp_client_manager.get_all_tools()
        
        # 按服务器分组显示工具
        server_tools = {}
        for tool_name, tool in all_tools.items():
            server_name = tool.server_name
            if server_name not in server_tools:
                server_tools[server_name] = []
            server_tools[server_name].append(tool)
        
        for server_name, tools in server_tools.items():
            server_config = mcp_config_manager.get_server_config(server_name)
            description = server_config.get("description", "") if server_config else ""
            
            info_lines.append(f"### {server_name}")
            if description:
                info_lines.append(f"**描述**: {description}")
            info_lines.append(f"**连接类型**: {server_config.get('connection_type', 'unknown')}")
            info_lines.append("**可用工具**:")
            
            for tool in tools:
                info_lines.append(f"- **{tool.name}**: {tool.description}")
                if tool.input_schema and "properties" in tool.input_schema:
                    params = list(tool.input_schema["properties"].keys())
                    info_lines.append(f"  - 参数: {', '.join(params)}")
            
            info_lines.append("")
        
        # 显示资源信息
        all_resources = mcp_client_manager.get_all_resources()
        if all_resources:
            info_lines.append("### 可用资源")
            for uri, resource in all_resources.items():
                info_lines.append(f"- **{resource.name}** ({resource.server_name})")
                info_lines.append(f"  - URI: {uri}")
                info_lines.append(f"  - 描述: {resource.description}")
                info_lines.append(f"  - 类型: {resource.mime_type}")
        
        return "\n".join(info_lines)
        
    except Exception as e:
        logger.error(f"获取MCP工具信息失败: {e}")
        return f"获取工具信息失败: {str(e)}"


def get_mcp_tools_list() -> List[Dict[str, Any]]:
    """
    获取MCP工具列表
    
    Returns:
        List[Dict[str, Any]]: 工具列表
    """
    try:
        if not mcp_config_manager.mcp_enabled:
            return []
        
        all_tools = mcp_client_manager.get_all_tools()
        
        tools_list = []
        for tool_name, tool in all_tools.items():
            tools_list.append({
                "name": tool.name,
                "description": tool.description,
                "server_name": tool.server_name,
                "input_schema": tool.input_schema
            })
        
        return tools_list
        
    except Exception as e:
        logger.error(f"获取MCP工具列表失败: {e}")
        return []


def get_mcp_resources_list() -> List[Dict[str, Any]]:
    """
    获取MCP资源列表
    
    Returns:
        List[Dict[str, Any]]: 资源列表
    """
    try:
        if not mcp_config_manager.mcp_enabled:
            return []
        
        all_resources = mcp_client_manager.get_all_resources()
        
        resources_list = []
        for uri, resource in all_resources.items():
            resources_list.append({
                "uri": resource.uri,
                "name": resource.name,
                "description": resource.description,
                "mime_type": resource.mime_type,
                "server_name": resource.server_name
            })
        
        return resources_list
        
    except Exception as e:
        logger.error(f"获取MCP资源列表失败: {e}")
        return []


def check_mcp_status() -> Dict[str, Any]:
    """
    检查MCP状态
    
    Returns:
        Dict[str, Any]: MCP状态信息
    """
    try:
        status = {
            "mcp_enabled": mcp_config_manager.mcp_enabled,
            "enabled_servers": [],
            "available_tools": 0,
            "available_resources": 0,
            "server_status": {}
        }
        
        if mcp_config_manager.mcp_enabled:
            enabled_servers = mcp_config_manager.get_enabled_servers()
            status["enabled_servers"] = list(enabled_servers.keys())
            
            # 检查每个服务器的连接状态
            for server_name in enabled_servers:
                client = mcp_client_manager.get_client(server_name)
                if client:
                    connected = client.connect() if hasattr(client, 'connect') else False
                    status["server_status"][server_name] = {
                        "connected": connected,
                        "tools_count": len(client.tools) if hasattr(client, 'tools') else 0,
                        "resources_count": len(client.resources) if hasattr(client, 'resources') else 0
                    }
                    
                    if connected:
                        status["available_tools"] += len(client.tools) if hasattr(client, 'tools') else 0
                        status["available_resources"] += len(client.resources) if hasattr(client, 'resources') else 0
                else:
                    status["server_status"][server_name] = {
                        "connected": False,
                        "tools_count": 0,
                        "resources_count": 0
                    }
        
        return status
        
    except Exception as e:
        logger.error(f"检查MCP状态失败: {e}")
        return {
            "mcp_enabled": False,
            "enabled_servers": [],
            "available_tools": 0,
            "available_resources": 0,
            "server_status": {},
            "error": str(e)
        }
