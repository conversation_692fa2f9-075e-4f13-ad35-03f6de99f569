"""
简化MCP客户端 - 为配置页面提供简化的MCP管理接口
"""
import logging
from typing import Dict, Any, Optional

from .mcp_config_manager import mcp_config_manager

logger = logging.getLogger(__name__)


class SimpleMCPConfig:
    """简化的MCP配置管理"""
    
    def __init__(self):
        self.current_server = "files"  # 默认服务器
    
    def is_mcp_enabled(self) -> bool:
        """检查MCP是否启用"""
        return mcp_config_manager.mcp_enabled
    
    def toggle_mcp(self, enabled: bool) -> bool:
        """切换MCP开关"""
        return mcp_config_manager.toggle_mcp_global(enabled)
    
    def set_current_server(self, server_name: str) -> bool:
        """设置当前服务器"""
        if server_name in mcp_config_manager.get_all_servers():
            self.current_server = server_name
            return True
        return False
    
    def get_current_server(self) -> str:
        """获取当前服务器"""
        return self.current_server
    
    def get_server_info(self, server_name: str) -> Optional[Dict[str, Any]]:
        """获取服务器信息"""
        return mcp_config_manager.get_server_config(server_name)


# 简化配置实例
mcp_config = SimpleMCPConfig()

# 为了兼容现有代码，也导出配置管理器
from .mcp_config_manager import mcp_config_manager
