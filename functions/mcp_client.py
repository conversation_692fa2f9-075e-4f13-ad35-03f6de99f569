"""
MCP客户端 - 简化版MCP协议实现
"""
import json
import subprocess
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from .mcp_config_manager import mcp_config_manager

logger = logging.getLogger(__name__)


@dataclass
class MCPTool:
    """MCP工具信息"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    server_name: str


class MCPClient:
    """简化版MCP客户端"""

    def __init__(self, server_name: str, command: str, args: List[str]):
        self.server_name = server_name
        self.command = command
        self.args = args
        self.process = None
        self.tools = {}
        self.connected = False

    def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            if self.connected:
                return True

            # 启动MCP服务器进程
            self.process = subprocess.Popen(
                [self.command] + self.args,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0
            )

            # 发送初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {"tools": {}},
                    "clientInfo": {
                        "name": "efficiency-mcp-client",
                        "version": "1.0.0"
                    }
                }
            }

            self._send_request(init_request)
            response = self._read_response()

            if response and response.get("result"):
                self.connected = True
                # 获取工具列表
                self._discover_tools()
                logger.info(f"MCP服务器 {self.server_name} 连接成功")
                return True
            else:
                logger.error(f"MCP服务器 {self.server_name} 初始化失败")
                return False

        except Exception as e:
            logger.error(f"连接MCP服务器 {self.server_name} 失败: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                self.process.kill()
            finally:
                self.process = None
        self.connected = False

    def _send_request(self, request: Dict[str, Any]):
        """发送请求到MCP服务器"""
        if not self.process or not self.process.stdin:
            raise Exception("MCP服务器未连接")

        request_str = json.dumps(request) + "\n"
        self.process.stdin.write(request_str)
        self.process.stdin.flush()

    def _read_response(self) -> Optional[Dict[str, Any]]:
        """读取MCP服务器响应"""
        if not self.process or not self.process.stdout:
            return None

        try:
            line = self.process.stdout.readline()
            if line:
                return json.loads(line.strip())
        except Exception as e:
            logger.error(f"读取MCP响应失败: {e}")
        return None

    def _discover_tools(self):
        """发现可用工具"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }

            self._send_request(request)
            response = self._read_response()

            if response and response.get("result") and "tools" in response["result"]:
                for tool_info in response["result"]["tools"]:
                    tool = MCPTool(
                        name=tool_info["name"],
                        description=tool_info.get("description", ""),
                        input_schema=tool_info.get("inputSchema", {}),
                        server_name=self.server_name
                    )
                    self.tools[tool.name] = tool

        except Exception as e:
            logger.error(f"发现工具失败: {e}")

    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, Any]:
        """调用工具"""
        try:
            if not self.connected:
                if not self.connect():
                    return False, "MCP服务器未连接"

            if tool_name not in self.tools:
                return False, f"工具 {tool_name} 不存在"

            request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }

            self._send_request(request)
            response = self._read_response()

            if response and "result" in response:
                return True, response["result"]
            elif response and "error" in response:
                return False, response["error"]["message"]
            else:
                return False, "未收到有效响应"

        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            return False, str(e)


class MCPManager:
    """MCP管理器"""

    def __init__(self):
        self.clients = {}

    def get_client(self, server_name: str):
        """获取或创建MCP客户端"""
        if server_name in self.clients:
            return self.clients[server_name]

        # 从配置创建客户端
        server_config = mcp_config_manager.get_server_config(server_name)
        if not server_config:
            return None

        client = MCPClient(
            server_name=server_name,
            command=server_config["command"],
            args=server_config["args"]
        )

        self.clients[server_name] = client
        return client

    def get_all_tools(self) -> Dict[str, MCPTool]:
        """获取所有可用工具"""
        all_tools = {}

        if not mcp_config_manager.mcp_enabled:
            return all_tools

        enabled_servers = mcp_config_manager.get_enabled_servers()

        for server_name in enabled_servers:
            client = self.get_client(server_name)
            if client and client.connect():
                all_tools.update(client.tools)

        return all_tools

    def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, Any]:
        """调用指定服务器的工具"""
        if not mcp_config_manager.mcp_enabled:
            return False, "MCP功能未启用"

        if not mcp_config_manager.is_server_enabled(server_name):
            return False, f"MCP服务 {server_name} 未启用"

        client = self.get_client(server_name)
        if not client:
            return False, f"无法创建MCP客户端: {server_name}"

        return client.call_tool(tool_name, arguments)

    def disconnect_all(self):
        """断开所有连接"""
        for client in self.clients.values():
            client.disconnect()
        self.clients.clear()


# 全局MCP管理器
mcp_manager = MCPManager()
