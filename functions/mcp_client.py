"""
MCP客户端 - 实现MCP协议的客户端功能
"""
import json
import asyncio
import subprocess
import logging
import time
import requests
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading

from .mcp_config_manager import mcp_config_manager

logger = logging.getLogger(__name__)


@dataclass
class MCPTool:
    """MCP工具信息"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    server_name: str


@dataclass
class MCPResource:
    """MCP资源信息"""
    uri: str
    name: str
    description: str
    mime_type: str
    server_name: str


class MCPStdioClient:
    """MCP STDIO客户端"""
    
    def __init__(self, server_name: str, command: str, args: List[str]):
        self.server_name = server_name
        self.command = command
        self.args = args
        self.process = None
        self.tools = {}
        self.resources = {}
        self.connected = False
        self._lock = threading.Lock()
    
    def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            with self._lock:
                if self.connected:
                    return True
                
                # 启动MCP服务器进程
                self.process = subprocess.Popen(
                    [self.command] + self.args,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=0
                )
                
                # 发送初始化请求
                init_request = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {},
                            "resources": {}
                        },
                        "clientInfo": {
                            "name": "efficiency-mcp-client",
                            "version": "1.0.0"
                        }
                    }
                }
                
                self._send_request(init_request)
                response = self._read_response()
                
                if response and response.get("result"):
                    self.connected = True
                    # 获取工具和资源列表
                    self._discover_tools()
                    self._discover_resources()
                    logger.info(f"MCP服务器 {self.server_name} 连接成功")
                    return True
                else:
                    logger.error(f"MCP服务器 {self.server_name} 初始化失败")
                    return False
                    
        except Exception as e:
            logger.error(f"连接MCP服务器 {self.server_name} 失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        with self._lock:
            if self.process:
                try:
                    self.process.terminate()
                    self.process.wait(timeout=5)
                except:
                    self.process.kill()
                finally:
                    self.process = None
            self.connected = False
    
    def _send_request(self, request: Dict[str, Any]):
        """发送请求到MCP服务器"""
        if not self.process or not self.process.stdin:
            raise Exception("MCP服务器未连接")
        
        request_str = json.dumps(request) + "\n"
        self.process.stdin.write(request_str)
        self.process.stdin.flush()
    
    def _read_response(self) -> Optional[Dict[str, Any]]:
        """读取MCP服务器响应"""
        if not self.process or not self.process.stdout:
            return None
        
        try:
            line = self.process.stdout.readline()
            if line:
                return json.loads(line.strip())
        except Exception as e:
            logger.error(f"读取MCP响应失败: {e}")
        return None
    
    def _discover_tools(self):
        """发现可用工具"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            self._send_request(request)
            response = self._read_response()
            
            if response and response.get("result") and "tools" in response["result"]:
                for tool_info in response["result"]["tools"]:
                    tool = MCPTool(
                        name=tool_info["name"],
                        description=tool_info.get("description", ""),
                        input_schema=tool_info.get("inputSchema", {}),
                        server_name=self.server_name
                    )
                    self.tools[tool.name] = tool
                    
        except Exception as e:
            logger.error(f"发现工具失败: {e}")
    
    def _discover_resources(self):
        """发现可用资源"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "resources/list"
            }
            
            self._send_request(request)
            response = self._read_response()
            
            if response and response.get("result") and "resources" in response["result"]:
                for resource_info in response["result"]["resources"]:
                    resource = MCPResource(
                        uri=resource_info["uri"],
                        name=resource_info.get("name", ""),
                        description=resource_info.get("description", ""),
                        mime_type=resource_info.get("mimeType", ""),
                        server_name=self.server_name
                    )
                    self.resources[resource.uri] = resource
                    
        except Exception as e:
            logger.error(f"发现资源失败: {e}")
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, Any]:
        """调用工具"""
        try:
            if not self.connected:
                if not self.connect():
                    return False, "MCP服务器未连接"
            
            if tool_name not in self.tools:
                return False, f"工具 {tool_name} 不存在"
            
            request = {
                "jsonrpc": "2.0",
                "id": int(time.time()),
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            self._send_request(request)
            response = self._read_response()
            
            if response and "result" in response:
                return True, response["result"]
            elif response and "error" in response:
                return False, response["error"]["message"]
            else:
                return False, "未收到有效响应"
                
        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            return False, str(e)
    
    def get_resource(self, uri: str) -> Tuple[bool, Any]:
        """获取资源"""
        try:
            if not self.connected:
                if not self.connect():
                    return False, "MCP服务器未连接"
            
            request = {
                "jsonrpc": "2.0",
                "id": int(time.time()),
                "method": "resources/read",
                "params": {
                    "uri": uri
                }
            }
            
            self._send_request(request)
            response = self._read_response()
            
            if response and "result" in response:
                return True, response["result"]
            elif response and "error" in response:
                return False, response["error"]["message"]
            else:
                return False, "未收到有效响应"
                
        except Exception as e:
            logger.error(f"获取资源 {uri} 失败: {e}")
            return False, str(e)


class MCPHttpClient:
    """MCP HTTP客户端"""
    
    def __init__(self, server_name: str, api_key: str, base_url: str = "https://api.mcp.run"):
        self.server_name = server_name
        self.api_key = api_key
        self.base_url = base_url
        self.tools = {}
        self.resources = {}
        self.connected = False
    
    def connect(self) -> bool:
        """连接到MCP HTTP服务器"""
        try:
            # 获取服务器信息
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.base_url}/servers/{self.server_name}/info",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                self.connected = True
                # 获取工具和资源列表
                self._discover_tools()
                self._discover_resources()
                logger.info(f"MCP HTTP服务器 {self.server_name} 连接成功")
                return True
            else:
                logger.error(f"MCP HTTP服务器 {self.server_name} 连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"连接MCP HTTP服务器 {self.server_name} 失败: {e}")
            return False
    
    def _discover_tools(self):
        """发现可用工具"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.base_url}/servers/{self.server_name}/tools",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                tools_data = response.json()
                for tool_info in tools_data.get("tools", []):
                    tool = MCPTool(
                        name=tool_info["name"],
                        description=tool_info.get("description", ""),
                        input_schema=tool_info.get("inputSchema", {}),
                        server_name=self.server_name
                    )
                    self.tools[tool.name] = tool
                    
        except Exception as e:
            logger.error(f"发现HTTP工具失败: {e}")
    
    def _discover_resources(self):
        """发现可用资源"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.base_url}/servers/{self.server_name}/resources",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                resources_data = response.json()
                for resource_info in resources_data.get("resources", []):
                    resource = MCPResource(
                        uri=resource_info["uri"],
                        name=resource_info.get("name", ""),
                        description=resource_info.get("description", ""),
                        mime_type=resource_info.get("mimeType", ""),
                        server_name=self.server_name
                    )
                    self.resources[resource.uri] = resource
                    
        except Exception as e:
            logger.error(f"发现HTTP资源失败: {e}")
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, Any]:
        """调用工具"""
        try:
            if not self.connected:
                if not self.connect():
                    return False, "MCP HTTP服务器未连接"
            
            if tool_name not in self.tools:
                return False, f"工具 {tool_name} 不存在"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            response = requests.post(
                f"{self.base_url}/servers/{self.server_name}/tools/call",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return True, response.json()
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            logger.error(f"调用HTTP工具 {tool_name} 失败: {e}")
            return False, str(e)
    
    def get_resource(self, uri: str) -> Tuple[bool, Any]:
        """获取资源"""
        try:
            if not self.connected:
                if not self.connect():
                    return False, "MCP HTTP服务器未连接"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.base_url}/servers/{self.server_name}/resources",
                headers=headers,
                params={"uri": uri},
                timeout=30
            )
            
            if response.status_code == 200:
                return True, response.json()
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            logger.error(f"获取HTTP资源 {uri} 失败: {e}")
            return False, str(e)


class MCPClientManager:
    """MCP客户端管理器"""

    def __init__(self):
        self.clients = {}
        self._executor = ThreadPoolExecutor(max_workers=5)

    def get_client(self, server_name: str):
        """获取或创建MCP客户端"""
        if server_name in self.clients:
            return self.clients[server_name]

        # 从配置创建客户端
        server_config = mcp_config_manager.get_server_config(server_name)
        if not server_config:
            return None

        connection_type = server_config.get("connection_type", "stdio")

        if connection_type == "stdio":
            client = MCPStdioClient(
                server_name=server_name,
                command=server_config["command"],
                args=server_config["args"]
            )
        elif connection_type == "http":
            client = MCPHttpClient(
                server_name=server_name,
                api_key=server_config["api_key"],
                base_url=server_config.get("base_url", "https://api.mcp.run")
            )
        else:
            logger.error(f"不支持的连接类型: {connection_type}")
            return None

        self.clients[server_name] = client
        return client

    def get_all_tools(self) -> Dict[str, MCPTool]:
        """获取所有可用工具"""
        all_tools = {}

        if not mcp_config_manager.mcp_enabled:
            return all_tools

        enabled_servers = mcp_config_manager.get_enabled_servers()

        for server_name in enabled_servers:
            client = self.get_client(server_name)
            if client and client.connect():
                all_tools.update(client.tools)

        return all_tools

    def get_all_resources(self) -> Dict[str, MCPResource]:
        """获取所有可用资源"""
        all_resources = {}

        if not mcp_config_manager.mcp_enabled:
            return all_resources

        enabled_servers = mcp_config_manager.get_enabled_servers()

        for server_name in enabled_servers:
            client = self.get_client(server_name)
            if client and client.connect():
                all_resources.update(client.resources)

        return all_resources

    def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, Any]:
        """调用指定服务器的工具"""
        if not mcp_config_manager.mcp_enabled:
            return False, "MCP功能未启用"

        if not mcp_config_manager.is_server_enabled(server_name):
            return False, f"MCP服务 {server_name} 未启用"

        client = self.get_client(server_name)
        if not client:
            return False, f"无法创建MCP客户端: {server_name}"

        return client.call_tool(tool_name, arguments)

    def get_resource(self, server_name: str, uri: str) -> Tuple[bool, Any]:
        """获取指定服务器的资源"""
        if not mcp_config_manager.mcp_enabled:
            return False, "MCP功能未启用"

        if not mcp_config_manager.is_server_enabled(server_name):
            return False, f"MCP服务 {server_name} 未启用"

        client = self.get_client(server_name)
        if not client:
            return False, f"无法创建MCP客户端: {server_name}"

        return client.get_resource(uri)

    def auto_call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, Any]:
        """自动选择服务器调用工具"""
        if not mcp_config_manager.mcp_enabled:
            return False, "MCP功能未启用"

        # 查找拥有该工具的服务器
        all_tools = self.get_all_tools()

        if tool_name not in all_tools:
            return False, f"工具 {tool_name} 不存在"

        tool = all_tools[tool_name]
        return self.call_tool(tool.server_name, tool_name, arguments)

    def disconnect_all(self):
        """断开所有连接"""
        for client in self.clients.values():
            if hasattr(client, 'disconnect'):
                client.disconnect()
        self.clients.clear()

    def __del__(self):
        """析构函数"""
        self.disconnect_all()


# 全局MCP客户端管理器
mcp_client_manager = MCPClientManager()
