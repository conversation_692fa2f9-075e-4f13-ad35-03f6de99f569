"""
MCP服务配置页面 - 简化版
"""
import json
import streamlit as st
from functions.simple_mcp_client import mcp_config_manager
from functions.mcp_tools import check_mcp_status, get_all_mcp_tools_info


def render_mcp_config_page():
    """渲染MCP配置页面"""
    st.title("🔌 MCP 服务配置")

    # === MCP 全局开关 ===
    st.header("🔄 MCP 功能控制")

    # 创建开关
    current_mcp_status = mcp_config_manager.mcp_enabled
    new_mcp_status = st.toggle(
        "启用MCP功能",
        value=current_mcp_status,
        help="开启后，AI将能够使用MCP服务提供的工具"
    )

    # 只有当状态发生变化时才执行操作
    if new_mcp_status != current_mcp_status:
        mcp_config_manager.toggle_mcp_global(new_mcp_status)
        if new_mcp_status:
            st.success("✅ MCP功能已启用")
        else:
            st.info("⚪ MCP功能已禁用")
        st.rerun()

    # 显示当前状态
    if current_mcp_status:
        st.info("MCP功能当前已启用，AI可以使用MCP工具")
    else:
        st.warning("MCP功能当前已禁用，AI无法使用MCP工具")
        return  # 如果MCP功能关闭，不显示后续内容

    # 获取服务器列表
    servers = mcp_config_manager.get_all_servers()

    # === MCP 服务列表 ===
    st.header("📋 MCP 服务列表")

    if not servers:
        st.info("暂无MCP服务配置")
        st.markdown("**默认配置包含:**")
        st.markdown("- files: 文件系统工具")
    else:
        # 获取MCP状态信息
        mcp_status = check_mcp_status()

        for server_name, server_config in servers.items():
            enabled = server_config.get("enabled", False)
            connection_type = server_config.get("connection_type", "stdio")
            description = server_config.get("description", "")

            # 状态和类型显示
            status_icon = "🟢" if enabled else "⚪"
            type_icon = "📡" if connection_type == "stdio" else "🔗"
            status_text = "已启用" if enabled else "已禁用"

            # 检查连接状态
            server_status = mcp_status.get("server_status", {}).get(server_name, {})
            connected = server_status.get("connected", False)
            connection_icon = "✅" if connected else "❌" if enabled else "⚪"

            col1, col2 = st.columns([4, 1])

            with col1:
                # 服务名称和描述
                st.markdown(f"### {type_icon} {server_name}")

                # 显示服务描述
                if description:
                    st.caption(f"💡 {description}")
                else:
                    st.caption("💡 文件系统操作工具")

                # 显示连接状态
                if enabled:
                    if connected:
                        tools_count = server_status.get("tools_count", 0)
                        st.caption(f"{connection_icon} 已连接 (工具: {tools_count})")
                    else:
                        st.caption(f"{connection_icon} 连接失败或未连接")

                # 命令信息
                command_info = f"`{server_config.get('command', 'N/A')} {' '.join(server_config.get('args', []))}`"
                st.markdown(f"**命令**: {command_info}")

                # 状态显示
                st.markdown(f"**状态**: {status_icon} {status_text}")

            with col2:
                new_enabled = st.toggle("启用", value=enabled, key=f"toggle_{server_name}", label_visibility="collapsed")

                # 只有当状态发生变化时才执行操作
                if new_enabled != enabled:
                    mcp_config_manager.toggle_server_status(server_name, new_enabled)
                    if new_enabled:
                        st.success(f"✅ 已启用 {server_name}")
                    else:
                        st.info(f"⚪ 已禁用 {server_name}")
                    st.rerun()

    # === MCP工具信息 ===
    if mcp_config_manager.mcp_enabled:
        st.header("🛠️ 可用工具")

        # 显示工具信息
        tools_info = get_all_mcp_tools_info()
        if tools_info:
            st.markdown(tools_info)
        else:
            st.info("暂无可用工具信息，请启用至少一个MCP服务")







# 如果直接运行此文件，则显示配置页面
if __name__ == "__main__":
    render_mcp_config_page()
