"""
MCP配置页面 - 参考Cline实现的简洁版本
"""
import streamlit as st
from functions.mcp_config_manager import mcp_config_manager
from functions.mcp_tools import check_mcp_status


def render_mcp_config_page():
    """渲染MCP配置页面 - 参考Cline的简洁设计"""
    st.title("MCP Configuration")

    # 获取服务器列表
    servers = mcp_config_manager.get_all_servers()

    if not servers:
        st.info("No MCP servers configured")
        return

    # 获取MCP状态信息
    mcp_status = check_mcp_status()

    # 为每个服务器创建一行
    for server_name, server_config in servers.items():
        enabled = server_config.get("enabled", False)

        # 检查连接状态
        server_status = mcp_status.get("server_status", {}).get(server_name, {})
        connected = server_status.get("connected", False) if enabled else False

        # 确定状态点的颜色
        if not enabled:
            status_color = "⚪"  # 灰色 - 禁用
            status_text = "Disabled"
        elif connected:
            status_color = "🟢"  # 绿色 - 连接成功
            status_text = "Connected"
        else:
            status_color = "🔴"  # 红色 - 连接失败
            status_text = "Failed"

        # 创建一行布局：状态点 + 服务名称 + 开关
        col1, col2, col3 = st.columns([1, 6, 2])

        with col1:
            st.markdown(f"<div style='font-size: 20px; text-align: center;'>{status_color}</div>",
                       unsafe_allow_html=True)

        with col2:
            st.markdown(f"**{server_name}**")
            st.caption(f"Status: {status_text}")

        with col3:
            new_enabled = st.toggle(
                "Enable",
                value=enabled,
                key=f"toggle_{server_name}",
                label_visibility="collapsed"
            )

            # 只有当状态发生变化时才执行操作
            if new_enabled != enabled:
                success = mcp_config_manager.toggle_server_status(server_name, new_enabled)
                if success:
                    st.rerun()
                else:
                    st.error(f"Failed to toggle {server_name}")

        # 添加分隔线
        st.divider()


if __name__ == "__main__":
    render_mcp_config_page()
