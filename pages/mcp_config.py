"""
MCP服务配置页面 - 基于LLaMa-MCP的简洁实现
"""
import json
import streamlit as st
from functions.simple_mcp_client import mcp_config_manager


def render_mcp_config_page():
    """渲染MCP配置页面"""
    # === MCP 全局开关 ===
    st.header("🔧 MCP 功能控制")

    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown("**MCP (Model Context Protocol) 功能总开关**")
        st.markdown("控制是否启用MCP工具集成功能")

    with col2:
        current_mcp_status = mcp_config_manager.mcp_enabled
        new_mcp_status = st.toggle("启用MCP", value=current_mcp_status, key="mcp_global_toggle")

        if new_mcp_status != current_mcp_status:
            mcp_config_manager.toggle_mcp_global(new_mcp_status)
            if new_mcp_status:
                st.success("✅ MCP功能已开启")
            else:
                st.warning("⚠️ MCP功能已关闭")
            st.rerun()

    # 如果MCP功能关闭，显示提示信息
    if not mcp_config_manager.mcp_enabled:
        st.info("💡 MCP功能已关闭。开启后可以使用外部工具增强AI助手能力。")
        return

    # 获取所有服务器配置
    try:
        servers = mcp_config_manager.get_all_servers()
    except Exception as e:
        st.error(f"获取MCP服务配置失败: {e}")
        return

    # === MCP 服务列表 ===
    st.header("📋 MCP 服务列表")

    if not servers:
        st.info("暂无MCP服务配置")
        st.markdown("**默认配置包含:**")
        st.markdown("- files: 文件系统工具")
        st.markdown("- playwright: 浏览器自动化工具")
        st.markdown("- duckduckgo-remote: 搜索工具")
        st.markdown("- chinarailway: 中国铁路查询")
        st.markdown("- hotnews: 热点新闻")
    else:
        for server_name, server_config in servers.items():
            enabled = server_config.get("enabled", False)
            connection_type = server_config.get("connection_type", "stdio")

            # 状态和类型显示
            status_icon = "🟢" if enabled else "⚪"
            type_icon = "🔗" if connection_type == "http" else "📡"
            status_text = "已启用" if enabled else "已禁用"

            col1, col2 = st.columns([4, 1])

            with col1:
                st.write(f"{status_icon} {type_icon} **{server_name}** - {status_text}")

                # 显示服务器配置信息
                if connection_type == "stdio":
                    command = server_config.get("command", "")
                    args = server_config.get("args", [])
                    if command and args:
                        st.caption(f"📡 STDIO: {command} {' '.join(args[:2])}...")
                elif connection_type == "http":
                    server_name_config = server_config.get("server_name", "")
                    if server_name_config:
                        st.caption(f"🔗 HTTP: {server_name_config}")

                # 显示服务描述
                descriptions = {
                    "files": "文件系统操作工具",
                    "playwright": "浏览器自动化和网页操作",
                    "duckduckgo-remote": "DuckDuckGo搜索引擎",
                    "chinarailway": "中国铁路信息查询",
                    "hotnews": "热点新闻获取"
                }
                if server_name in descriptions:
                    st.caption(f"💡 {descriptions[server_name]}")

            with col2:
                new_enabled = st.toggle("启用", value=enabled, key=f"toggle_{server_name}", label_visibility="collapsed")

                # 只有当状态发生变化时才执行操作
                if new_enabled != enabled:
                    mcp_config_manager.toggle_server_status(server_name, new_enabled)
                    if new_enabled:
                        st.success(f"✅ 已启用 {server_name}")
                    else:
                        st.info(f"⚪ 已禁用 {server_name}")
                    st.rerun()

    # === MCP配置管理 ===
    st.header("⚙️ 配置管理")

    col1, col2 = st.columns(2)

    with col1:
        # 导出配置
        if st.button("📤 导出配置"):
            try:
                config_json = json.dumps(mcp_config_manager.config, indent=2, ensure_ascii=False)
                st.download_button(
                    label="下载配置文件",
                    data=config_json,
                    file_name="mcp_config.json",
                    mime="application/json"
                )
            except Exception as e:
                st.error(f"导出配置失败: {e}")

    with col2:
        # 导入配置
        uploaded_file = st.file_uploader("📥 导入配置文件", type=["json"])
        if uploaded_file is not None:
            try:
                config = json.load(uploaded_file)
                if "mcpServers" in config:
                    # 更新配置
                    mcp_config_manager.config = config
                    mcp_config_manager._save_config(config)
                    st.success("✅ 配置导入成功")
                    st.rerun()
                else:
                    st.error("❌ 配置文件格式错误，缺少 mcpServers 字段")
            except json.JSONDecodeError:
                st.error("❌ 配置文件格式错误，请上传有效的JSON文件")
            except Exception as e:
                st.error(f"❌ 导入配置失败: {e}")

    # === 使用说明 ===
    with st.expander("📖 使用说明", expanded=False):
        st.markdown("""
        **MCP (Model Context Protocol) 功能说明:**

        1. **全局开关**: 控制是否启用MCP功能
        2. **服务管理**: 启用/禁用具体的MCP服务
        3. **配置导入/导出**: 备份和恢复MCP配置

        **支持的MCP服务类型:**

        **📡 STDIO服务 (本地运行):**
        - **files**: 文件系统操作工具，支持文件读写、目录操作
        - **playwright**: 浏览器自动化工具，支持网页操作、截图、数据提取

        **🔗 HTTP服务 (远程调用):**
        - **duckduckgo-remote**: DuckDuckGo搜索引擎，支持网络搜索
        - **chinarailway**: 中国铁路信息查询，支持车次、票价查询
        - **hotnews**: 热点新闻获取，支持实时新闻获取

        **配置格式说明:**
        - STDIO服务需要配置 `command` 和 `args`
        - HTTP服务需要配置 `server_name` 和 `api_key`
        - 所有服务都支持 `enabled` 开关控制

        **注意事项:**
        - STDIO服务需要Node.js环境和相应的npm包
        - HTTP服务需要有效的API密钥
        - 首次使用可能需要下载依赖包
        - 建议在容器环境中测试MCP功能
        """)




# 如果直接运行此文件，则显示配置页面
if __name__ == "__main__":
    render_mcp_config_page()
