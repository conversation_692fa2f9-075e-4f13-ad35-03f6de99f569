"""
MCP配置页面 - 基于LLaMa-MCP的简洁实现
"""
import streamlit as st
from functions.mcp_client_simple import mcp_config
from functions.mcp_tools_simple import get_mcp_tools_info


def render_mcp_config_page():
    """渲染MCP配置页面 - 简洁版本"""
    
    st.title("🔧 MCP 配置")
    
    # === MCP 全局开关 ===
    st.header("🎛️ MCP 功能控制")
    
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown("**MCP (Model Context Protocol) 功能开关**")
        st.markdown("控制是否启用MCP工具集成功能")
    
    with col2:
        current_status = mcp_config.is_mcp_enabled()
        new_status = st.toggle("启用MCP", value=current_status, key="mcp_global_toggle")
        
        if new_status != current_status:
            mcp_config.toggle_mcp(new_status)
            if new_status:
                st.success("✅ MCP功能已开启")
            else:
                st.warning("⚠️ MCP功能已关闭")
            st.rerun()
    
    # 如果MCP功能关闭，显示提示信息
    if not mcp_config.is_mcp_enabled():
        st.info("💡 MCP功能已关闭。开启后可以使用外部工具增强AI助手能力。")
        return
    
    # === MCP 服务选择 ===
    st.header("🔄 MCP 服务选择")
    
    current_server = mcp_config.current_server
    
    # 服务选择
    server_options = {
        "files": "📁 文件系统服务 - 文件读写、目录操作",
        "playwright": "🌐 浏览器服务 - 网页操作、截图、数据提取"
    }
    
    selected_server = st.selectbox(
        "选择MCP服务",
        options=list(server_options.keys()),
        index=list(server_options.keys()).index(current_server),
        format_func=lambda x: server_options[x],
        key="server_selector"
    )
    
    if selected_server != current_server:
        mcp_config.set_current_server(selected_server)
        st.success(f"✅ 已切换到 {server_options[selected_server]}")
        st.rerun()
    
    # === 当前状态显示 ===
    st.header("📊 当前状态")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric("MCP状态", "🟢 已启用" if mcp_config.is_mcp_enabled() else "🔴 已禁用")
    
    with col2:
        st.metric("当前服务", f"📡 {current_server}")
    
    # === 工具信息 ===
    st.header("🛠️ 工具信息")
    
    tools_info = get_mcp_tools_info()
    st.markdown(tools_info)
    
    # === 使用说明 ===
    with st.expander("📖 使用说明", expanded=False):
        st.markdown("""
        **MCP (Model Context Protocol) 功能说明:**
        
        **基于LLaMa-MCP的简洁实现:**
        - 🎛️ **全局开关**: 一键开启/关闭MCP功能
        - 🔄 **服务切换**: 在不同MCP服务间切换
        - 📡 **按需启动**: MCP服务自动安装和启动
        
        **可用服务:**
        - **📁 files**: 文件系统操作工具，支持文件读写、目录操作
        - **🌐 playwright**: 浏览器自动化工具，支持网页操作、截图、数据提取
        
        **特点:**
        - ✅ **简洁高效**: 基于LLaMa-MCP的简洁架构
        - ✅ **按需安装**: 使用npx -y自动下载和运行MCP服务
        - ✅ **无需预装**: 不需要在容器中预装MCP包
        - ✅ **配置驱动**: 通过页面控制MCP服务
        
        **注意事项:**
        - 首次使用某个服务时可能需要下载依赖包
        - 需要Node.js环境支持
        - 建议在容器环境中使用
        """)


# 如果直接运行此文件，则显示配置页面
if __name__ == "__main__":
    render_mcp_config_page()
