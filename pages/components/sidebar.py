"""
    __init__.py
"""
import os
import logging
import streamlit as st
from openai import OpenAI
from .reflect import reflect_on_conversation


@st.dialog("Select Session")
def select_session_dialog():
    """"""
    session_name = st.selectbox("Select Session", ["New Session"] + [s["session_name"]
                                for s in st.session_state.session_manager.list()])
    if st.button("Select"):
        if session_name == "New Session":
            st.session_state.session = st.session_state.session_manager.new({"session_name": "New Session"})
        else:
            st.session_state.session = st.session_state.session_manager.get({"session_name": session_name})
        st.rerun()


def render_chat_sidebar():
    """"""
    st.header("Session Management")
    if st.button("Select Session"):
        select_session_dialog()

    # MCP Configuration
    st.header("MCP Configuration")

    # 导入MCP相关模块
    try:
        from functions.mcp_config_manager import mcp_config_manager
        from functions.mcp_tools import check_mcp_status

        # 配置JSON文件编辑
        with st.expander("Edit Config", expanded=False):
            import json
            config_text = st.text_area(
                "MCP Configuration JSON",
                value=json.dumps(mcp_config_manager.config, indent=2, ensure_ascii=False),
                height=200,
                key="mcp_config_text"
            )

            col1, col2 = st.columns(2)
            with col1:
                if st.button("Save Config"):
                    try:
                        new_config = json.loads(config_text)
                        mcp_config_manager.config = new_config
                        mcp_config_manager._save_config()
                        st.success("Config saved")
                        st.rerun()
                    except json.JSONDecodeError:
                        st.error("Invalid JSON")
                    except Exception as e:
                        st.error(f"Error: {e}")

            with col2:
                if st.button("Reset"):
                    st.rerun()

        # MCP服务列表
        st.subheader("MCP Services")

        servers = mcp_config_manager.get_all_servers()
        if not servers:
            st.info("No MCP servers configured")
        else:
            # 获取MCP状态信息
            mcp_status = check_mcp_status()

            for server_name, server_config in servers.items():
                enabled = server_config.get("enabled", False)

                # 检查连接状态
                server_status = mcp_status.get("server_status", {}).get(server_name, {})
                connected = server_status.get("connected", False) if enabled else False

                # 确定状态点的颜色
                if not enabled:
                    status_color = "⚪"  # 灰色 - 禁用
                elif connected:
                    status_color = "🟢"  # 绿色 - 连接成功
                else:
                    status_color = "🔴"  # 红色 - 连接失败

                # 创建一行布局：服务名称 + 开关文字 + 状态点
                col1, col2, col3 = st.columns([3, 3, 1])

                with col1:
                    st.text(server_name)

                with col2:
                    # 使用按钮显示enabled/disabled状态
                    button_text = "enabled" if enabled else "disabled"
                    button_color = "normal" if enabled else "secondary"

                    if st.button(button_text, key=f"btn_{server_name}", type=button_color):
                        success = mcp_config_manager.toggle_server_status(server_name, not enabled)
                        if success:
                            st.rerun()

                with col3:
                    st.markdown(f"<div style='font-size: 20px; text-align: center;'>{status_color}</div>",
                               unsafe_allow_html=True)

    except ImportError:
        st.error("MCP modules not available")



    st.header("Reflect on Conversation")
    if prompt := st.chat_input("Something should be reflected on..."):
        try:
            reflection = reflect_on_conversation(st.session_state.quoted_history, prompt)
        except Exception as e:
            st.error(f"An error occurred: {str(e)}")
            logging.error(f"Reflection error: {str(e)}", exc_info=True)
    # 默认折叠下面的输入框。展示当前保存的参数
    # API密钥设置部分
    st.header("API Configuration")

    # 获取当前配置（优先session_state，其次环境变量）
    current_config = {
        "base_url": st.session_state.get("chat_base_url", os.getenv("BASE_URL", "")),
        "api_key": st.session_state.get("api_key", os.getenv("API_KEY", "")),
        "model": st.session_state.get("selected_model", os.getenv("MODEL", ""))
    }

    # 默认显示当前参数（只要有任一配置存在就显示）
    if any(current_config.values()):
        cols = st.columns([1, 3])

        cols[0].write("Base URL:")
        cols[1].write(current_config["base_url"] or "Not set")

        cols[0].write("API Key:")
        cols[1].write("*" * (len(current_config["api_key"]) if current_config["api_key"] else 10))

        cols[0].write("Selected Model:")
        cols[1].write(current_config["model"] or "Not set")

    # 点击按钮显示/隐藏修改表单
    if st.button("Modify API Configuration"):
        st.session_state.show_api_config = not st.session_state.get("show_api_config", False)

    # 修改配置表单
    if st.session_state.get("show_api_config", False):
        with st.form("api_config_form"):
            new_base_url = st.text_input(
                "API Base URL",
                value=current_config["base_url"],
                help="Enter your API base URL (e.g., https://api.example.com)"
            )

            new_api_key = st.text_input(
                "API Key",
                value=current_config["api_key"],
                type="password",
                help="Enter your API key"
            )

            # 表单提交按钮
            submitted = st.form_submit_button("Save Configuration")
            if submitted:
                if new_base_url and new_api_key:
                    # 保存新配置
                    st.session_state.chat_base_url = new_base_url
                    st.session_state.api_key = new_api_key
                    os.environ["BASE_URL"] = new_base_url
                    os.environ["API_KEY"] = new_api_key

                    # 尝试获取模型列表
                    try:
                        chat_client = OpenAI(
                            base_url=new_base_url,
                            api_key=new_api_key
                        )
                        model_list = [model.id for model in chat_client.models.list().data]

                        if model_list:
                            st.session_state.model_list = model_list
                            st.session_state.selected_model = model_list[0]
                            os.environ["MODEL"] = model_list[0]
                            current_config["model"] = model_list[0]

                        st.success("Configuration saved successfully!")
                        st.session_state.show_api_config = False
                        st.rerun()
                    except Exception as e:
                        st.error(f"Failed to initialize client: {str(e)}")
                else:
                    st.error("Please provide both Base URL and API Key")

    # 模型选择（仅在配置有效且有模型列表时显示）
    if "model_list" in st.session_state and st.session_state.model_list:
        current_model = current_config["model"]
        model_index = st.session_state.model_list.index(
            current_model) if current_model in st.session_state.model_list else 0

        selected_model = st.selectbox(
            "Select Model",
            options=st.session_state.model_list,
            index=model_index
        )

        if selected_model != current_model:
            st.session_state.selected_model = selected_model
            os.environ["MODEL"] = selected_model
            st.rerun()

    # 测试连接按钮（仅在配置完整时显示）
    if all(current_config.values()):
        if st.button("Test Connection"):
            try:
                client = OpenAI(
                    base_url=current_config["base_url"],
                    api_key=current_config["api_key"]
                )
                models = client.models.list()
                st.success("Connection successful!")

                # 显示可用模型（最多5个）
                st.write("Available models:")
                for model in models.data[:5]:
                    st.write(f"- {model.id}")
            except Exception as e:
                st.error(f"Connection failed: {str(e)}")